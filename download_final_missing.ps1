# Script to download the final missing packages: zlib and autoconf
# Based on the directory listings provided by user

$downloadDir = "cygwin_binary_packages"
$baseUrl = "https://cygwin.mirror.constant.com/x86_64/release"

# Final missing packages with correct paths
$finalPackages = @(
    @{
        "name" = "zlib-1.3.1-1.tar.zst"
        "url" = "$baseUrl/zlib/zlib-1.3.1-1.tar.zst"
        "description" = "zlib compression library (main package)"
    },
    @{
        "name" = "zlib0-1.3.1-1.tar.zst"
        "url" = "$baseUrl/zlib/zlib0/zlib0-1.3.1-1.tar.zst"
        "description" = "zlib0 runtime library"
    },
    @{
        "name" = "autoconf-15-2.tar.zst"
        "url" = "$baseUrl/autoconf/autoconf-15-2.tar.zst"
        "description" = "autoconf configure script generator"
    },
    @{
        "name" = "autoconf-13-1.tar.zst"
        "url" = "$baseUrl/autoconf/autoconf-13-1.tar.zst"
        "description" = "autoconf configure script generator (alternative version)"
    }
)

Write-Host "=========================================="
Write-Host "Downloading Final Missing Packages"
Write-Host "=========================================="
Write-Host "Target directory: $downloadDir"
Write-Host ""

$successCount = 0
$totalAttempts = 0

foreach ($package in $finalPackages) {
    $outputPath = Join-Path $downloadDir $package.name
    $totalAttempts++

    # Skip if already exists
    if (Test-Path $outputPath) {
        Write-Host "SKIP: $($package.name) already exists"
        $successCount++
        continue
    }

    try {
        Write-Host "Downloading: $($package.name)"
        Write-Host "Description: $($package.description)"
        Write-Host "URL: $($package.url)"

        Invoke-WebRequest -Uri $package.url -OutFile $outputPath -UseBasicParsing -TimeoutSec 60
        Write-Host "SUCCESS: Downloaded $($package.name)"
        $successCount++

        # If we got autoconf, we can skip trying the alternative version
        if ($package.name -like "*autoconf*" -and $successCount -gt 0) {
            Write-Host "Got autoconf, skipping alternative versions"
            break
        }
    }
    catch {
        Write-Host "FAILED: $($package.name) - $($_.Exception.Message)"
        if (Test-Path $outputPath) {
            Remove-Item $outputPath -Force
        }
    }
    Write-Host ""
}

Write-Host "=========================================="
Write-Host "FINAL DOWNLOAD SUMMARY"
Write-Host "=========================================="
Write-Host "Successfully downloaded: $successCount packages"

# Show complete inventory
$allFiles = Get-ChildItem $downloadDir -Filter "*.tar.*" -ErrorAction SilentlyContinue | Sort-Object Name
Write-Host ""
Write-Host "COMPLETE PACKAGE INVENTORY:"
Write-Host "Total packages: $($allFiles.Count)"
Write-Host ""

$packageTypes = @{
    "Build Tools" = @("make", "autoconf")
    "Compilers" = @("gcc-core", "gcc-g++", "libgcc")
    "Development Tools" = @("flex", "bison", "git", "wget")
    "Libraries" = @("zlib")
}

foreach ($category in $packageTypes.Keys) {
    Write-Host "${category}:"
    $found = $false
    foreach ($file in $allFiles) {
        foreach ($pattern in $packageTypes[$category]) {
            if ($file.Name -like "*$pattern*") {
                Write-Host "  + $($file.Name)"
                $found = $true
            }
        }
    }
    if (-not $found) {
        Write-Host "  (none found)"
    }
    Write-Host ""
}

# Final status check
$hasCompiler = ($allFiles | Where-Object { $_.Name -like "*gcc-core*" }).Count -gt 0
$hasCppCompiler = ($allFiles | Where-Object { $_.Name -like "*gcc-g++*" }).Count -gt 0
$hasMake = ($allFiles | Where-Object { $_.Name -like "*make*" }).Count -gt 0
$hasGit = ($allFiles | Where-Object { $_.Name -like "*git*" }).Count -gt 0
$hasAutoconf = ($allFiles | Where-Object { $_.Name -like "*autoconf*" }).Count -gt 0
$hasZlib = ($allFiles | Where-Object { $_.Name -like "*zlib*" }).Count -gt 0

Write-Host "=========================================="
Write-Host "READINESS CHECK"
Write-Host "=========================================="
Write-Host "Essential packages:"
Write-Host "  C Compiler (gcc): $(if ($hasCompiler) { 'YES' } else { 'NO' })"
Write-Host "  C++ Compiler (g++): $(if ($hasCppCompiler) { 'YES' } else { 'NO' })"
Write-Host "  Build Tool (make): $(if ($hasMake) { 'YES' } else { 'NO' })"
Write-Host "  Version Control (git): $(if ($hasGit) { 'YES' } else { 'NO' })"
Write-Host ""
Write-Host "Important packages:"
Write-Host "  Configure Tool (autoconf): $(if ($hasAutoconf) { 'YES' } else { 'NO' })"
Write-Host "  Compression Library (zlib): $(if ($hasZlib) { 'YES' } else { 'NO' })"

$essentialCount = @($hasCompiler, $hasCppCompiler, $hasMake, $hasGit) | Where-Object { $_ } | Measure-Object | Select-Object -ExpandProperty Count
$totalEssential = 4

Write-Host ""
Write-Host "Essential packages: $essentialCount/$totalEssential"

if ($essentialCount -eq $totalEssential -and $hasAutoconf) {
    Write-Host ""
    Write-Host "EXCELLENT! You have ALL packages needed for Verilator!"
    Write-Host "You can proceed with complete confidence."
} elseif ($essentialCount -eq $totalEssential) {
    Write-Host ""
    Write-Host "VERY GOOD! You have all essential packages."
    Write-Host "Missing autoconf might not be critical - try proceeding anyway."
} elseif ($essentialCount -ge 3) {
    Write-Host ""
    Write-Host "GOOD! You have most essential packages."
    Write-Host "Try proceeding - some missing packages might have alternatives."
} else {
    Write-Host ""
    Write-Host "You're missing some essential packages."
    Write-Host "Try proceeding anyway - the configure script will tell you what's critical."
}

Write-Host ""
Write-Host "=========================================="
Write-Host "NEXT STEPS"
Write-Host "=========================================="
Write-Host "1. Copy packages to Cygwin:"
Write-Host "   copy_to_cygwin.bat"
Write-Host ""
Write-Host "2. Install packages:"
Write-Host "   bash install_downloaded_packages.sh"
Write-Host ""
Write-Host "3. Verify installations:"
Write-Host "   gcc --version && g++ --version && make --version"
Write-Host ""
Write-Host "4. Install Verilator:"
Write-Host "   git clone https://github.com/verilator/verilator"
Write-Host "   cd verilator && autoconf && ./configure && make"
Write-Host ""
Write-Host "You're ready to build Verilator!"
