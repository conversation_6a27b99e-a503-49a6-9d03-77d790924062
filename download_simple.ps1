# Simple PowerShell script to download missing Cygwin packages
# No Unicode characters to avoid parsing issues

# Create downloads directory
$downloadDir = "cygwin_binary_packages"
if (!(Test-Path $downloadDir)) {
    New-Item -ItemType Directory -Path $downloadDir
}

# Base URL
$baseUrl = "https://cygwin.mirror.constant.com/x86_64/release"

# Direct download URLs based on the directory structure you provided
$downloads = @(
    @{
        "name" = "gcc-core-12.4.0-3.tar.zst"
        "url" = "$baseUrl/gcc/gcc-core/gcc-core-12.4.0-3.tar.zst"
    },
    @{
        "name" = "gcc-g++-12.4.0-3.tar.zst"
        "url" = "$baseUrl/gcc/gcc-g%2B%2B/gcc-g++-12.4.0-3.tar.zst"
    },
    @{
        "name" = "libgcc1-12.4.0-3.tar.zst"
        "url" = "$baseUrl/gcc/libgcc1/libgcc1-12.4.0-3.tar.zst"
    },
    @{
        "name" = "zlib0-1.3.1-1.tar.zst"
        "url" = "$baseUrl/zlib/zlib0-1.3.1-1.tar.zst"
    },
    @{
        "name" = "autoconf-15-2.tar.zst"
        "url" = "$baseUrl/autoconf/autoconf-15-2.tar.zst"
    }
)

Write-Host "Downloading missing Cygwin packages..."
Write-Host "Base URL: $baseUrl"
Write-Host "Download directory: $downloadDir"
Write-Host ""

$successCount = 0

foreach ($download in $downloads) {
    $outputPath = Join-Path $downloadDir $download.name
    
    if (Test-Path $outputPath) {
        Write-Host "SKIP: $($download.name) already exists"
        $successCount++
        continue
    }
    
    try {
        Write-Host "Downloading: $($download.name)"
        Write-Host "URL: $($download.url)"
        Invoke-WebRequest -Uri $download.url -OutFile $outputPath -UseBasicParsing -TimeoutSec 60
        Write-Host "SUCCESS: Downloaded $($download.name)"
        $successCount++
    }
    catch {
        Write-Host "FAILED: $($download.name) - $($_.Exception.Message)"
        if (Test-Path $outputPath) {
            Remove-Item $outputPath -Force
        }
    }
    Write-Host ""
}

Write-Host "=========================================="
Write-Host "DOWNLOAD SUMMARY"
Write-Host "=========================================="
Write-Host "Successfully downloaded: $successCount out of $($downloads.Count) packages"

# List downloaded files
$files = Get-ChildItem $downloadDir -Filter "*.tar.zst" -ErrorAction SilentlyContinue
if ($files) {
    Write-Host ""
    Write-Host "Downloaded files:"
    foreach ($file in $files) {
        Write-Host "  $($file.Name)"
    }
}

Write-Host ""
Write-Host "=========================================="
Write-Host "INSTALLATION INSTRUCTIONS"
Write-Host "=========================================="

if ($files.Count -gt 0) {
    Write-Host "1. Copy files to Cygwin home directory:"
    Write-Host "   copy cygwin_binary_packages\*.tar.zst C:\cygwin64\home\%USERNAME%\"
    Write-Host ""
    Write-Host "2. In Cygwin terminal, extract packages:"
    Write-Host "   cd ~"
    Write-Host "   for file in *.tar.zst; do"
    Write-Host "     echo ""Extracting $file..."""
    Write-Host "     tar --zstd -xvf ""$file"" -C /"
    Write-Host "   done"
    Write-Host ""
    Write-Host "3. Verify installations:"
    Write-Host "   gcc --version"
    Write-Host "   g++ --version"
    Write-Host "   autoconf --version"
    Write-Host ""
    Write-Host "4. Install Verilator:"
    Write-Host "   git clone https://github.com/verilator/verilator"
    Write-Host "   cd verilator"
    Write-Host "   unset VERILATOR_ROOT"
    Write-Host "   git pull"
    Write-Host "   autoconf"
    Write-Host "   ./configure"
    Write-Host "   make -j 4"
    Write-Host ""
    Write-Host "SUCCESS! You should now have all required packages."
} else {
    Write-Host "No packages were downloaded."
    Write-Host "Try downloading manually from: $baseUrl"
}

Write-Host ""
Write-Host "Note: These packages use .tar.zst format."
Write-Host "Make sure zstd is available in Cygwin to extract them."
