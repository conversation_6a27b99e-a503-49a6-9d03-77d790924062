#!/bin/bash
# Script to install the downloaded Cygwin packages
# Run this script in your Cygwin terminal

echo "==================================="
echo "Cygwin Package Installation Script"
echo "==================================="

# Check if we're in Cygwin
if [[ ! "$OSTYPE" =~ cygwin ]]; then
    echo "WARNING: This script should be run in Cygwin terminal"
    echo "Current OSTYPE: $OSTYPE"
fi

# Create backup directory
BACKUP_DIR="$HOME/package_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"
echo "Backup directory created: $BACKUP_DIR"

# List of packages to install
PACKAGES=(
    "wget-1.25.0-1.tar.xz"
    "make-4.4-1.tar.xz"
    "flex-2.6.4-2.tar.xz"
    "bison-3.8.2-1.tar.xz"
    "git-2.45.1-1.tar.xz"
)

echo ""
echo "Checking for package files..."

# Check if package files exist
FOUND_PACKAGES=()
for package in "${PACKAGES[@]}"; do
    if [[ -f "$HOME/$package" ]]; then
        echo "✓ Found: $package"
        FOUND_PACKAGES+=("$package")
    else
        echo "✗ Missing: $package"
    fi
done

if [[ ${#FOUND_PACKAGES[@]} -eq 0 ]]; then
    echo ""
    echo "ERROR: No package files found in $HOME"
    echo "Please copy the .tar.xz files from cygwin_binary_packages/ to your Cygwin home directory first:"
    echo "  cp cygwin_binary_packages/*.tar.xz /home/<USER>/"
    exit 1
fi

echo ""
echo "Found ${#FOUND_PACKAGES[@]} package(s) to install"

# Ask for confirmation
read -p "Do you want to proceed with installation? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Installation cancelled."
    exit 0
fi

echo ""
echo "Starting installation..."

# Install each package
SUCCESS_COUNT=0
TOTAL_COUNT=${#FOUND_PACKAGES[@]}

for package in "${FOUND_PACKAGES[@]}"; do
    echo ""
    echo "Installing $package..."
    
    # Move original file to backup
    cp "$HOME/$package" "$BACKUP_DIR/"
    
    # Extract package
    if tar -xvf "$HOME/$package" -C /; then
        echo "✓ Successfully installed $package"
        ((SUCCESS_COUNT++))
        
        # Remove the package file after successful installation
        rm "$HOME/$package"
    else
        echo "✗ Failed to install $package"
        echo "  Package file preserved in $HOME"
    fi
done

echo ""
echo "==================================="
echo "Installation Summary"
echo "==================================="
echo "Successfully installed: $SUCCESS_COUNT/$TOTAL_COUNT packages"
echo "Backup location: $BACKUP_DIR"

# Verify installations
echo ""
echo "Verifying installations..."

# Check each tool
TOOLS=(
    "wget:wget --version"
    "make:make --version"
    "flex:flex --version"
    "bison:bison --version"
    "git:git --version"
)

echo ""
for tool_info in "${TOOLS[@]}"; do
    tool_name="${tool_info%%:*}"
    tool_command="${tool_info#*:}"
    
    echo -n "Checking $tool_name... "
    if command -v "$tool_name" >/dev/null 2>&1; then
        echo "✓ Available"
        # Show version in a compact way
        version_output=$($tool_command 2>&1 | head -1)
        echo "  $version_output"
    else
        echo "✗ Not found"
    fi
done

# Check PATH
echo ""
echo "Current PATH:"
echo "$PATH" | tr ':' '\n' | grep -E "(usr|bin)" | head -5

# Suggest next steps
echo ""
echo "==================================="
echo "Next Steps"
echo "==================================="

if [[ $SUCCESS_COUNT -eq $TOTAL_COUNT ]]; then
    echo "✓ All available packages installed successfully!"
    echo ""
    echo "You still need to install these missing packages:"
    echo "  - autoconf (for configure script generation)"
    echo "  - gcc-core (C compiler)"
    echo "  - gcc-g++ (C++ compiler)"
    echo "  - libgcc1 (GCC runtime library)"
    echo "  - zlib0 (compression library)"
    echo ""
    echo "Try these approaches:"
    echo "1. Search for alternative package names:"
    echo "   find /usr/bin -name '*gcc*' -o -name '*autoconf*'"
    echo ""
    echo "2. Try to proceed with Verilator anyway:"
    echo "   git clone https://github.com/verilator/verilator"
    echo "   cd verilator"
    echo "   ./configure"
    echo ""
    echo "3. Check the installation guide: cygwin_installation_guide.md"
else
    echo "Some packages failed to install. Check the errors above."
    echo "Package files are preserved in your home directory."
fi

echo ""
echo "Installation script completed."
