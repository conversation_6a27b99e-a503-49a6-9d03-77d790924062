# PowerShell script to download Cygwin packages from multiple mirrors
# This script tries different mirrors and package naming conventions

# Create downloads directory
$downloadDir = "cygwin_binary_packages"
if (!(Test-Path $downloadDir)) {
    New-Item -ItemType Directory -Path $downloadDir
}

# Multiple Cygwin mirrors to try
$mirrors = @(
    "https://mirrors.kernel.org/sourceware/cygwin/x86_64/release",
    "https://mirrors.xmission.com/cygwin/x86_64/release",
    "https://cygwin.mirror.constant.com/x86_64/release",
    "https://mirror.clarkson.edu/cygwin/x86_64/release"
)

# Current working packages with correct versions from cygwin.com
$packageVariants = @{
    "autoconf" = @(
        @{ "dir" = "autoconf2.7"; "file" = "autoconf2.7-2.72-1.tar.xz" },
        @{ "dir" = "autoconf2.7"; "file" = "autoconf2.7-2.71-2.tar.xz" },
        @{ "dir" = "autoconf2.7"; "file" = "autoconf2.7-2.71-1.tar.xz" }
    )
    "gcc-core" = @(
        @{ "dir" = "gcc-core"; "file" = "gcc-core-12.4.0-3.tar.xz" },
        @{ "dir" = "gcc-core"; "file" = "gcc-core-11.5.0-1.tar.xz" },
        @{ "dir" = "gcc-core"; "file" = "gcc-core-11.4.0-1.tar.xz" }
    )
    "gcc-g++" = @(
        @{ "dir" = "gcc-g++"; "file" = "gcc-g++-12.4.0-3.tar.xz" },
        @{ "dir" = "gcc-g++"; "file" = "gcc-g++-11.5.0-1.tar.xz" },
        @{ "dir" = "gcc-g++"; "file" = "gcc-g++-11.4.0-1.tar.xz" }
    )
    "libgcc1" = @(
        @{ "dir" = "libgcc1"; "file" = "libgcc1-12.4.0-3.tar.xz" },
        @{ "dir" = "libgcc1"; "file" = "libgcc1-11.5.0-1.tar.xz" },
        @{ "dir" = "libgcc1"; "file" = "libgcc1-11.4.0-1.tar.xz" }
    )
    "zlib" = @(
        @{ "dir" = "zlib0"; "file" = "zlib0-1.3.1-1.tar.xz" },
        @{ "dir" = "zlib0"; "file" = "zlib0-1.3-1.tar.xz" },
        @{ "dir" = "zlib0"; "file" = "zlib0-1.2.13-1.tar.xz" }
    )
}

function Try-DownloadPackage {
    param(
        [string]$mirror,
        [string]$packageDir,
        [string]$filename
    )

    $url = "$mirror/$packageDir/$filename"
    $outputPath = Join-Path $downloadDir $filename

    # Skip if already downloaded
    if (Test-Path $outputPath) {
        Write-Host "Already exists: $filename"
        return $true
    }

    try {
        Write-Host "Trying: $url"
        Invoke-WebRequest -Uri $url -OutFile $outputPath -UseBasicParsing -TimeoutSec 30
        Write-Host "SUCCESS: Downloaded $filename"
        return $true
    }
    catch {
        Write-Host "FAILED: $($_.Exception.Message)"
        # Clean up partial download
        if (Test-Path $outputPath) {
            Remove-Item $outputPath -Force
        }
        return $false
    }
}

Write-Host "Downloading missing Cygwin packages from multiple mirrors..."
Write-Host "Download directory: $downloadDir"

$successCount = 0
$totalPackages = $packageVariants.Keys.Count

foreach ($packageName in $packageVariants.Keys) {
    Write-Host "`n" + "="*50
    Write-Host "Downloading: $packageName"
    Write-Host "="*50

    $downloaded = $false
    $variants = $packageVariants[$packageName]

    # Try each variant
    foreach ($variant in $variants) {
        if ($downloaded) { break }

        Write-Host "`nTrying variant: $($variant.file)"

        # Try each mirror
        foreach ($mirror in $mirrors) {
            if ($downloaded) { break }

            if (Try-DownloadPackage -mirror $mirror -packageDir $variant.dir -filename $variant.file) {
                $downloaded = $true
                $successCount++
                break
            }
        }
    }

    if (-not $downloaded) {
        Write-Warning "Failed to download any variant of $packageName"
    }
}

Write-Host "`n" + "="*60
Write-Host "DOWNLOAD SUMMARY"
Write-Host "="*60
Write-Host "Successfully downloaded: $successCount/$totalPackages packages"

# List all downloaded files
$allFiles = Get-ChildItem $downloadDir -Filter "*.tar.xz"
Write-Host "`nAll downloaded packages:"
foreach ($file in $allFiles) {
    Write-Host "  - $($file.Name)"
}

Write-Host "`n" + "="*60
Write-Host "INSTALLATION INSTRUCTIONS"
Write-Host "="*60
Write-Host "1. Copy all .tar.xz files to your Cygwin home directory:"
Write-Host "   cp cygwin_binary_packages/*.tar.xz /home/<USER>/"
Write-Host ""
Write-Host "2. In Cygwin terminal, extract all packages:"
Write-Host "   cd ~"
Write-Host "   for file in *.tar.xz; do"
Write-Host "     echo `"Extracting `$file...`""
Write-Host "     tar -xvf `"`$file`" -C /"
Write-Host "   done"
Write-Host ""
Write-Host "3. Verify installations:"
Write-Host "   wget --version"
Write-Host "   make --version"
Write-Host "   gcc --version"
Write-Host "   g++ --version"
Write-Host "   autoconf --version"
Write-Host "   flex --version"
Write-Host "   bison --version"
Write-Host ""
Write-Host "4. If any command is not found, check that /usr/bin is in your PATH:"
Write-Host "   echo `$PATH"
Write-Host "   export PATH=`$PATH:/usr/bin"

if ($successCount -eq $totalPackages) {
    Write-Host "`n✅ All packages downloaded successfully!"
} elseif ($successCount -gt 0) {
    Write-Host "`n⚠️  Some packages downloaded. You may need to find alternatives for missing ones."
} else {
    Write-Host "`n❌ No packages were downloaded. You may need to:"
    Write-Host "   - Try a different approach"
    Write-Host "   - Use Cygwin setup.exe if possible"
    Write-Host "   - Manually browse Cygwin mirrors"
}
