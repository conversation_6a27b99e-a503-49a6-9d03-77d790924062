# PowerShell script to download the missing Cygwin packages
# Based on actual mirror structure provided by user

# Create downloads directory
$downloadDir = "cygwin_binary_packages"
if (!(Test-Path $downloadDir)) {
    New-Item -ItemType Directory -Path $downloadDir
}

# Base mirror URL (you can change this to your preferred mirror)
$baseUrl = "https://cygwin.mirror.constant.com/x86_64/release"

# Packages to download with their correct paths
$packages = @(
    @{
        "name" = "gcc-core"
        "path" = "gcc/gcc-core"
        "files" = @("gcc-core-12.4.0-3.tar.zst", "gcc-core-11.5.0-1.tar.zst")
    },
    @{
        "name" = "gcc-g++"
        "path" = "gcc/gcc-g%2B%2B"  # URL encoded for gcc-g++
        "files" = @("gcc-g++-12.4.0-3.tar.zst", "gcc-g++-11.5.0-1.tar.zst")
    },
    @{
        "name" = "libgcc1"
        "path" = "gcc/libgcc1"
        "files" = @("libgcc1-12.4.0-3.tar.zst", "libgcc1-11.5.0-1.tar.zst")
    },
    @{
        "name" = "zlib0"
        "path" = "zlib"
        "files" = @("zlib0-1.3.1-1.tar.zst", "zlib0-1.3-1.tar.zst", "zlib0-1.2.13-1.tar.zst")
    },
    @{
        "name" = "autoconf"
        "path" = "autoconf"
        "files" = @("autoconf-15-2.tar.zst", "autoconf-13-1.tar.zst")
    }
)

function Download-Package {
    param(
        [string]$name,
        [string]$path,
        [string]$filename
    )

    $url = "$baseUrl/$path/$filename"
    $outputPath = Join-Path $downloadDir $filename

    # Skip if already downloaded
    if (Test-Path $outputPath) {
        Write-Host "✓ Already exists: $filename"
        return $true
    }

    try {
        Write-Host "Downloading $filename..."
        Write-Host "  URL: $url"
        Invoke-WebRequest -Uri $url -OutFile $outputPath -UseBasicParsing -TimeoutSec 60
        Write-Host "✓ SUCCESS: Downloaded $filename"
        return $true
    }
    catch {
        Write-Host "✗ FAILED: $filename - $($_.Exception.Message)"
        # Clean up partial download
        if (Test-Path $outputPath) {
            Remove-Item $outputPath -Force
        }
        return $false
    }
}

Write-Host "=========================================="
Write-Host "Downloading Missing Cygwin Packages"
Write-Host "=========================================="
Write-Host "Base URL: $baseUrl"
Write-Host "Download directory: $downloadDir"
Write-Host ""

$totalSuccess = 0
$totalAttempts = 0

foreach ($package in $packages) {
    Write-Host "Processing: $($package.name)"
    Write-Host "Path: $($package.path)"

    $packageSuccess = $false

    # Try each file variant for this package
    foreach ($file in $package.files) {
        $totalAttempts++
        if (Download-Package -name $package.name -path $package.path -filename $file) {
            $totalSuccess++
            $packageSuccess = $true
            break  # Stop trying other variants once we get one
        }
    }

    if (-not $packageSuccess) {
        Write-Host "⚠️  Could not download any variant of $($package.name)"
    }

    Write-Host ""
}

Write-Host "=========================================="
Write-Host "DOWNLOAD SUMMARY"
Write-Host "=========================================="
Write-Host "Successfully downloaded: $totalSuccess packages"

# List all downloaded files
$allFiles = Get-ChildItem $downloadDir -Filter "*.tar.zst" -ErrorAction SilentlyContinue
if ($allFiles) {
    Write-Host ""
    Write-Host "Downloaded packages:"
    foreach ($file in $allFiles) {
        Write-Host "  + $($file.Name)"
    }
}

Write-Host ""
Write-Host "=========================================="
Write-Host "NEXT STEPS"
Write-Host "=========================================="

if ($allFiles.Count -gt 0) {
    Write-Host "SUCCESS! You now have $($allFiles.Count) Cygwin packages ready to install."
    Write-Host ""
    Write-Host "Installation Instructions:"
    Write-Host ""
    Write-Host "1. Copy packages to Cygwin:"
    Write-Host "   copy cygwin_binary_packages\*.tar.zst C:\cygwin64\home\%USERNAME%\"
    Write-Host ""
    Write-Host "2. In Cygwin terminal, extract packages:"
    Write-Host "   cd ~"
    Write-Host "   # Install zstd if not available:"
    Write-Host "   # (zstd should be available in most recent Cygwin installations)"
    Write-Host ""
    Write-Host "   # Extract all packages:"
    Write-Host "   for file in *.tar.zst; do"
    Write-Host "     echo `"Extracting `$file...`""
    Write-Host "     tar --zstd -xvf `"`$file`" -C /"
    Write-Host "   done"
    Write-Host ""
    Write-Host "3. Verify installations:"
    Write-Host "   gcc --version"
    Write-Host "   g++ --version"
    Write-Host "   autoconf --version  # (if downloaded)"
    Write-Host ""
    Write-Host "4. Install Verilator:"
    Write-Host "   git clone https://github.com/verilator/verilator"
    Write-Host "   cd verilator"
    Write-Host "   unset VERILATOR_ROOT"
    Write-Host "   git pull"
    Write-Host "   autoconf"
    Write-Host "   ./configure"
    Write-Host "   make -j `$(nproc)"
    Write-Host ""
    Write-Host "Note: If zstd is not available in Cygwin, you may need to:"
    Write-Host "   - Install it via Cygwin setup if possible"
    Write-Host "   - Or convert .zst files to .xz format using online tools"

} else {
    Write-Host "No packages were downloaded successfully."
    Write-Host ""
    Write-Host "Troubleshooting options:"
    Write-Host "1. Check your internet connection"
    Write-Host "2. Try a different mirror"
    Write-Host "3. Manually download from: $baseUrl"
    Write-Host "4. Use Cygwin setup.exe if available"
}

Write-Host ""
Write-Host "Combined with your existing packages (wget, make, flex, bison, git),"
Write-Host "you should have everything needed for Verilator!"
