@echo off
R<PERSON> Batch script to copy downloaded packages to Cygwin home directory

echo ========================================
echo Copy Cygwin Packages to Home Directory
echo ========================================

REM Set default Cygwin installation path
set CYGWIN_ROOT=C:\cygwin64
set CYGWIN_HOME=%CYGWIN_ROOT%\home\%USERNAME%

echo.
echo Checking Cygwin installation...

REM Check if Cygwin is installed
if not exist "%CYGWIN_ROOT%" (
    echo ERROR: Cygwin not found at %CYGWIN_ROOT%
    echo.
    echo Please check your Cygwin installation path.
    echo Common locations:
    echo   C:\cygwin64
    echo   C:\cygwin
    echo   D:\cygwin64
    echo.
    set /p CYGWIN_ROOT="Enter your Cygwin installation path: "
    set CYGWIN_HOME=!CYGWIN_ROOT!\home\%USERNAME%
)

REM Check if home directory exists
if not exist "%CYGWIN_HOME%" (
    echo ERROR: Cygwin home directory not found at %CYGWIN_HOME%
    echo.
    echo Creating home directory...
    mkdir "%CYGWIN_HOME%" 2>nul
    if errorlevel 1 (
        echo Failed to create home directory. Please check permissions.
        pause
        exit /b 1
    )
)

echo ✓ Cygwin found at: %CYGWIN_ROOT%
echo ✓ Home directory: %CYGWIN_HOME%

echo.
echo Checking for downloaded packages...

REM Check if package directory exists
if not exist "cygwin_binary_packages" (
    echo ERROR: Package directory 'cygwin_binary_packages' not found
    echo.
    echo Please make sure you have run the download scripts first.
    pause
    exit /b 1
)

REM List available packages
echo.
echo Available packages:
dir /b cygwin_binary_packages\*.tar.xz 2>nul
if errorlevel 1 (
    echo No .tar.xz files found in cygwin_binary_packages directory
    pause
    exit /b 1
)

echo.
set /p CONFIRM="Copy these packages to Cygwin home directory? (Y/N): "
if /i not "%CONFIRM%"=="Y" (
    echo Operation cancelled.
    pause
    exit /b 0
)

echo.
echo Copying packages...

REM Copy packages
copy "cygwin_binary_packages\*.tar.xz" "%CYGWIN_HOME%\" >nul 2>&1
if errorlevel 1 (
    echo ERROR: Failed to copy packages
    echo.
    echo Please check:
    echo 1. You have write permissions to %CYGWIN_HOME%
    echo 2. Cygwin is not running (close all Cygwin terminals)
    echo 3. The package files exist in cygwin_binary_packages\
    pause
    exit /b 1
)

echo ✓ Packages copied successfully!

echo.
echo Verifying copied files...
dir /b "%CYGWIN_HOME%\*.tar.xz" 2>nul
if errorlevel 1 (
    echo WARNING: No .tar.xz files found in destination
) else (
    echo ✓ Files verified in destination
)

echo.
echo ========================================
echo Copy completed successfully!
echo ========================================
echo.
echo Next steps:
echo 1. Open Cygwin terminal
echo 2. Run the installation script:
echo    bash install_downloaded_packages.sh
echo.
echo Or manually extract packages:
echo    cd ~
echo    for file in *.tar.xz; do tar -xvf "$file" -C /; done
echo.
echo Files copied to: %CYGWIN_HOME%
echo.

pause
