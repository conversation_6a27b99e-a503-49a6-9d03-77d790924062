# PowerShell script to download Cygwin packages
# This script downloads the required packages for Verilator installation

# Create downloads directory
$downloadDir = "cygwin_packages"
if (!(Test-Path $downloadDir)) {
    New-Item -ItemType Directory -Path $downloadDir
}

# Cygwin mirror URL
$mirrorUrl = "https://mirrors.kernel.org/sourceware/cygwin/x86_64/release"

# Package list with their directories and patterns for BINARY packages (not source)
$packages = @{
    "wget" = @{
        "dir" = "wget"
        "pattern" = "wget-[0-9].*\.tar\.xz"
        "exclude" = "-src"
    }
    "gcc-g++" = @{
        "dir" = "gcc-g++"
        "pattern" = "gcc-g\+\+-[0-9].*\.tar\.xz"
        "exclude" = "-src"
    }
    "make" = @{
        "dir" = "make"
        "pattern" = "make-[0-9].*\.tar\.xz"
        "exclude" = "-src"
    }
    "autoconf" = @{
        "dir" = "autoconf"
        "pattern" = "autoconf-[0-9].*\.tar\.xz"
        "exclude" = "-src"
    }
    "flex" = @{
        "dir" = "flex"
        "pattern" = "flex-[0-9].*\.tar\.xz"
        "exclude" = "-src"
    }
    "bison" = @{
        "dir" = "bison"
        "pattern" = "bison-[0-9].*\.tar\.xz"
        "exclude" = "-src"
    }
    "libgcc1" = @{
        "dir" = "libgcc1"
        "pattern" = "libgcc1-[0-9].*\.tar\.xz"
        "exclude" = "-src"
    }
    "zlib0" = @{
        "dir" = "zlib0"
        "pattern" = "zlib0-[0-9].*\.tar\.xz"
        "exclude" = "-src"
    }
    "git" = @{
        "dir" = "git"
        "pattern" = "git-[0-9].*\.tar\.xz"
        "exclude" = "-src"
    }
}

function Get-LatestPackage {
    param(
        [string]$packageDir,
        [string]$pattern,
        [string]$exclude = ""
    )

    try {
        $url = "$mirrorUrl/$packageDir/"
        Write-Host "Checking $url for latest binary package matching $pattern"

        # Get directory listing
        $response = Invoke-WebRequest -Uri $url -UseBasicParsing
        $content = $response.Content

        # Extract package filenames matching pattern
        $matches = [regex]::Matches($content, $pattern)

        if ($matches.Count -gt 0) {
            # Filter out excluded patterns (like source packages)
            $filteredMatches = $matches | ForEach-Object { $_.Value } | Where-Object {
                if ($exclude -ne "") {
                    $_ -notlike "*$exclude*"
                } else {
                    $true
                }
            }

            if ($filteredMatches.Count -gt 0) {
                # Get the latest version (assuming lexicographic ordering works)
                $latestFile = ($filteredMatches | Sort-Object)[-1]
                Write-Host "Found: $latestFile"
                return $latestFile
            } else {
                Write-Warning "No binary packages found after filtering out $exclude patterns"
                return $null
            }
        }
        else {
            Write-Warning "No packages found matching $pattern in $packageDir"
            return $null
        }
    }
    catch {
        Write-Error "Failed to get package list for $packageDir`: $_"
        return $null
    }
}

function Download-Package {
    param(
        [string]$packageDir,
        [string]$filename
    )

    $url = "$mirrorUrl/$packageDir/$filename"
    $outputPath = Join-Path $downloadDir $filename

    try {
        Write-Host "Downloading $filename..."
        Invoke-WebRequest -Uri $url -OutFile $outputPath -UseBasicParsing
        Write-Host "Downloaded: $outputPath"
        return $true
    }
    catch {
        Write-Error "Failed to download $filename`: $_"
        return $false
    }
}

# Main download loop
Write-Host "Starting Cygwin package downloads..."
Write-Host "Download directory: $downloadDir"

$successCount = 0
$totalCount = $packages.Count

foreach ($packageName in $packages.Keys) {
    $package = $packages[$packageName]
    Write-Host "`nProcessing $packageName..."

    $latestFile = Get-LatestPackage -packageDir $package.dir -pattern $package.pattern -exclude $package.exclude

    if ($latestFile) {
        if (Download-Package -packageDir $package.dir -filename $latestFile) {
            $successCount++
        }
    }
}

Write-Host "`nDownload Summary:"
Write-Host "Successfully downloaded: $successCount/$totalCount packages"
Write-Host "Files saved to: $downloadDir"

if ($successCount -eq $totalCount) {
    Write-Host "`nAll packages downloaded successfully!"
    Write-Host "Next steps:"
    Write-Host "1. Copy these files to your Cygwin home directory"
    Write-Host "2. Extract them using tar in Cygwin terminal"
    Write-Host "3. Run the extraction commands provided in your instructions"
} else {
    Write-Host "`nSome downloads failed. Check the errors above."
}
