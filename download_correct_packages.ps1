# PowerShell script to download the correct Cygwin packages with proper paths and formats
# Based on actual mirror structure analysis

# Create downloads directory
$downloadDir = "cygwin_binary_packages"
if (!(Test-Path $downloadDir)) {
    New-Item -ItemType Directory -Path $downloadDir
}

# Working mirror
$mirror = "https://cygwin.mirror.constant.com/x86_64/release"

# Correct packages with actual paths and formats found on the mirror
$packages = @(
    @{
        "name" = "gcc-core"
        "url" = "$mirror/gcc/gcc-core/gcc-core-12.4.0-3.tar.zst"
        "file" = "gcc-core-12.4.0-3.tar.zst"
    },
    @{
        "name" = "gcc-g++"
        "url" = "$mirror/gcc/gcc-g%2B%2B/gcc-g++-12.4.0-3.tar.zst"
        "file" = "gcc-g++-12.4.0-3.tar.zst"
    },
    @{
        "name" = "libgcc1"
        "url" = "$mirror/gcc/libgcc1/libgcc1-12.4.0-3.tar.zst"
        "file" = "libgcc1-12.4.0-3.tar.zst"
    },
    @{
        "name" = "zlib0"
        "url" = "$mirror/zlib/zlib0-1.3.1-1.tar.zst"
        "file" = "zlib0-1.3.1-1.tar.zst"
    },
    @{
        "name" = "autoconf"
        "url" = "$mirror/autoconf/autoconf-15-2.tar.zst"
        "file" = "autoconf-15-2.tar.zst"
    }
)

# Alternative packages to try if main ones fail
$alternativePackages = @(
    @{
        "name" = "gcc-core-alt"
        "url" = "$mirror/gcc/gcc-core/gcc-core-11.5.0-1.tar.zst"
        "file" = "gcc-core-11.5.0-1.tar.zst"
    },
    @{
        "name" = "gcc-g++-alt"
        "url" = "$mirror/gcc/gcc-g%2B%2B/gcc-g++-11.5.0-1.tar.zst"
        "file" = "gcc-g++-11.5.0-1.tar.zst"
    },
    @{
        "name" = "libgcc1-alt"
        "url" = "$mirror/gcc/libgcc1/libgcc1-11.5.0-1.tar.zst"
        "file" = "libgcc1-11.5.0-1.tar.zst"
    },
    @{
        "name" = "zlib0-alt"
        "url" = "$mirror/zlib/zlib0-1.3-1.tar.zst"
        "file" = "zlib0-1.3-1.tar.zst"
    }
)

function Download-Package {
    param(
        [string]$name,
        [string]$url,
        [string]$filename
    )
    
    $outputPath = Join-Path $downloadDir $filename
    
    # Skip if already downloaded
    if (Test-Path $outputPath) {
        Write-Host "✓ Already exists: $filename"
        return $true
    }
    
    try {
        Write-Host "Downloading $name..."
        Write-Host "  URL: $url"
        Invoke-WebRequest -Uri $url -OutFile $outputPath -UseBasicParsing -TimeoutSec 60
        Write-Host "✓ SUCCESS: Downloaded $filename"
        return $true
    }
    catch {
        Write-Host "✗ FAILED: $name - $($_.Exception.Message)"
        # Clean up partial download
        if (Test-Path $outputPath) {
            Remove-Item $outputPath -Force
        }
        return $false
    }
}

Write-Host "=========================================="
Write-Host "Downloading Missing Cygwin Packages"
Write-Host "=========================================="
Write-Host "Mirror: $mirror"
Write-Host "Download directory: $downloadDir"
Write-Host ""

$successCount = 0
$totalCount = $packages.Count

# Try main packages first
foreach ($package in $packages) {
    if (Download-Package -name $package.name -url $package.url -filename $package.file) {
        $successCount++
    }
}

# Try alternatives for failed packages
if ($successCount -lt $totalCount) {
    Write-Host ""
    Write-Host "Trying alternative versions for failed downloads..."
    
    foreach ($package in $alternativePackages) {
        if (Download-Package -name $package.name -url $package.url -filename $package.file) {
            $successCount++
        }
    }
}

Write-Host ""
Write-Host "=========================================="
Write-Host "DOWNLOAD SUMMARY"
Write-Host "=========================================="
Write-Host "Successfully downloaded: $successCount packages"

# List all downloaded files
$allFiles = Get-ChildItem $downloadDir -Filter "*.tar.zst"
Write-Host ""
Write-Host "Downloaded packages:"
foreach ($file in $allFiles) {
    Write-Host "  ✓ $($file.Name)"
}

Write-Host ""
Write-Host "=========================================="
Write-Host "INSTALLATION INSTRUCTIONS"
Write-Host "=========================================="

if ($allFiles.Count -gt 0) {
    Write-Host "1. Install zstd in Cygwin (if not already installed):"
    Write-Host "   # zstd is needed to extract .tar.zst files"
    Write-Host ""
    Write-Host "2. Copy all .tar.zst files to your Cygwin home directory:"
    Write-Host "   cp cygwin_binary_packages/*.tar.zst /home/<USER>/"
    Write-Host ""
    Write-Host "3. In Cygwin terminal, extract all packages:"
    Write-Host "   cd ~"
    Write-Host "   for file in *.tar.zst; do"
    Write-Host "     echo `"Extracting `$file...`""
    Write-Host "     tar --zstd -xvf `"`$file`" -C /"
    Write-Host "   done"
    Write-Host ""
    Write-Host "4. Verify installations:"
    Write-Host "   gcc --version"
    Write-Host "   g++ --version"
    Write-Host "   autoconf --version"
    Write-Host ""
    Write-Host "5. If any command is not found, check PATH:"
    Write-Host "   echo `$PATH"
    Write-Host "   export PATH=`$PATH:/usr/bin:/usr/local/bin"
    Write-Host ""
    Write-Host "6. Proceed with Verilator installation:"
    Write-Host "   git clone https://github.com/verilator/verilator"
    Write-Host "   cd verilator"
    Write-Host "   unset VERILATOR_ROOT"
    Write-Host "   git pull"
    Write-Host "   autoconf"
    Write-Host "   ./configure"
    Write-Host "   make -j `$(nproc)"
    
    Write-Host ""
    Write-Host "✅ Ready for installation!"
} else {
    Write-Host "❌ No packages were downloaded."
    Write-Host ""
    Write-Host "Troubleshooting:"
    Write-Host "1. Check internet connection"
    Write-Host "2. Try running the script again"
    Write-Host "3. Manually browse: $mirror"
    Write-Host "4. Consider using Cygwin setup.exe if available"
}

Write-Host ""
Write-Host "Note: The packages use .tar.zst format (Zstandard compression)"
Write-Host "which is more efficient than .tar.xz. Make sure you have zstd"
Write-Host "available in Cygwin to extract them."
