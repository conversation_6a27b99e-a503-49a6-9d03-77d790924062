# PowerShell script to download specific known Cygwin binary packages
# This script downloads specific versions that are known to exist

# Create downloads directory
$downloadDir = "cygwin_binary_packages"
if (!(Test-Path $downloadDir)) {
    New-Item -ItemType Directory -Path $downloadDir
}

# Cygwin mirror URL
$mirrorUrl = "https://mirrors.kernel.org/sourceware/cygwin/x86_64/release"

# Specific packages with known filenames (these are more likely to exist)
$specificPackages = @(
    @{ "dir" = "wget"; "file" = "wget-1.25.0-1.tar.xz" },
    @{ "dir" = "make"; "file" = "make-4.4-1.tar.xz" },
    @{ "dir" = "autoconf"; "file" = "autoconf-15-2.tar.xz" },
    @{ "dir" = "flex"; "file" = "flex-2.6.4-2.tar.xz" },
    @{ "dir" = "bison"; "file" = "bison-3.8.2-1.tar.xz" },
    @{ "dir" = "git"; "file" = "git-2.45.1-1.tar.xz" },
    @{ "dir" = "gcc-core"; "file" = "gcc-core-11.4.0-1.tar.xz" },
    @{ "dir" = "gcc-g++"; "file" = "gcc-g++-11.4.0-1.tar.xz" },
    @{ "dir" = "libgcc1"; "file" = "libgcc1-11.4.0-1.tar.xz" },
    @{ "dir" = "zlib0"; "file" = "zlib0-1.3-1.tar.xz" }
)

function Download-SpecificPackage {
    param(
        [string]$packageDir,
        [string]$filename
    )
    
    $url = "$mirrorUrl/$packageDir/$filename"
    $outputPath = Join-Path $downloadDir $filename
    
    # Skip if already downloaded
    if (Test-Path $outputPath) {
        Write-Host "Already exists: $filename"
        return $true
    }
    
    try {
        Write-Host "Downloading $filename from $packageDir..."
        Invoke-WebRequest -Uri $url -OutFile $outputPath -UseBasicParsing
        Write-Host "Downloaded: $outputPath"
        return $true
    }
    catch {
        Write-Warning "Failed to download $filename from $packageDir`: $($_.Exception.Message)"
        return $false
    }
}

# Main download loop
Write-Host "Starting specific Cygwin binary package downloads..."
Write-Host "Download directory: $downloadDir"

$successCount = 0
$totalCount = $specificPackages.Count

foreach ($package in $specificPackages) {
    if (Download-SpecificPackage -packageDir $package.dir -filename $package.file) {
        $successCount++
    }
}

Write-Host "`nDownload Summary:"
Write-Host "Successfully downloaded: $successCount/$totalCount packages"
Write-Host "Files saved to: $downloadDir"

# Also try alternative versions if some failed
if ($successCount -lt $totalCount) {
    Write-Host "`nTrying alternative versions for failed downloads..."
    
    $alternativePackages = @(
        @{ "dir" = "gcc-core"; "file" = "gcc-core-10.2.0-1.tar.xz" },
        @{ "dir" = "gcc-g++"; "file" = "gcc-g++-10.2.0-1.tar.xz" },
        @{ "dir" = "libgcc1"; "file" = "libgcc1-10.2.0-1.tar.xz" },
        @{ "dir" = "autoconf"; "file" = "autoconf-13-1.tar.xz" },
        @{ "dir" = "zlib0"; "file" = "zlib0-1.2.13-1.tar.xz" }
    )
    
    foreach ($package in $alternativePackages) {
        if (Download-SpecificPackage -packageDir $package.dir -filename $package.file) {
            $successCount++
        }
    }
}

Write-Host "`nFinal Summary:"
Write-Host "Total packages downloaded: $successCount"
Write-Host "Files saved to: $downloadDir"

if ($successCount -gt 0) {
    Write-Host "`nNext steps:"
    Write-Host "1. Copy these files to your Cygwin home directory"
    Write-Host "2. In Cygwin terminal, extract them with:"
    Write-Host "   cd ~"
    Write-Host "   for file in *.tar.xz; do tar -xvf `$file -C /; done"
    Write-Host "3. Verify installation with: wget --version, make --version, etc."
} else {
    Write-Host "`nNo packages were downloaded successfully."
    Write-Host "You may need to manually browse the Cygwin mirror and download packages."
}
