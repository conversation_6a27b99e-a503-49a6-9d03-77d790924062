# Script to download autoconf packages
# Based on the autoconf package page information

$downloadDir = "cygwin_binary_packages"
$baseUrl = "https://cygwin.mirror.constant.com/x86_64/release"

# Autoconf packages to try
$autoconfPackages = @(
    @{
        "name" = "autoconf-15-2.tar.zst"
        "url" = "$baseUrl/autoconf/autoconf-15-2.tar.zst"
        "description" = "autoconf wrapper script (latest)"
    },
    @{
        "name" = "autoconf-15-1.tar.zst"
        "url" = "$baseUrl/autoconf/autoconf-15-1.tar.zst"
        "description" = "autoconf wrapper script (alternative)"
    },
    @{
        "name" = "autoconf-13-1.tar.zst"
        "url" = "$baseUrl/autoconf/autoconf-13-1.tar.zst"
        "description" = "autoconf wrapper script (older)"
    },
    @{
        "name" = "autoconf2.7-2.72-1.tar.zst"
        "url" = "$baseUrl/autoconf2.7/autoconf2.7-2.72-1.tar.zst"
        "description" = "autoconf 2.7 implementation"
    },
    @{
        "name" = "autoconf2.7-2.71-1.tar.zst"
        "url" = "$baseUrl/autoconf2.7/autoconf2.7-2.71-1.tar.zst"
        "description" = "autoconf 2.7 implementation (alternative)"
    }
)

Write-Host "=========================================="
Write-Host "Downloading Autoconf Packages"
Write-Host "=========================================="
Write-Host "Target directory: $downloadDir"
Write-Host ""

$successCount = 0

foreach ($package in $autoconfPackages) {
    $outputPath = Join-Path $downloadDir $package.name
    
    # Skip if already exists
    if (Test-Path $outputPath) {
        Write-Host "SKIP: $($package.name) already exists"
        $successCount++
        continue
    }
    
    try {
        Write-Host "Downloading: $($package.name)"
        Write-Host "Description: $($package.description)"
        Write-Host "URL: $($package.url)"
        
        Invoke-WebRequest -Uri $package.url -OutFile $outputPath -UseBasicParsing -TimeoutSec 60
        Write-Host "SUCCESS: Downloaded $($package.name)"
        $successCount++
        
        # If we got the main autoconf wrapper, we can stop
        if ($package.name -like "autoconf-*" -and $package.name -notlike "*autoconf2*") {
            Write-Host "Got autoconf wrapper, continuing to get autoconf2.7..."
        }
        
        # If we got autoconf2.7, we can stop trying alternatives
        if ($package.name -like "*autoconf2.7*") {
            Write-Host "Got autoconf2.7, stopping here"
            break
        }
    }
    catch {
        Write-Host "FAILED: $($package.name) - $($_.Exception.Message)"
        if (Test-Path $outputPath) {
            Remove-Item $outputPath -Force
        }
    }
    Write-Host ""
}

Write-Host "=========================================="
Write-Host "AUTOCONF DOWNLOAD SUMMARY"
Write-Host "=========================================="
Write-Host "Successfully downloaded: $successCount autoconf-related packages"

# Show final complete inventory
$allFiles = Get-ChildItem $downloadDir -Filter "*.tar.*" -ErrorAction SilentlyContinue | Sort-Object Name
Write-Host ""
Write-Host "COMPLETE FINAL INVENTORY:"
Write-Host "Total packages: $($allFiles.Count)"
Write-Host ""

foreach ($file in $allFiles) {
    $type = if ($file.Name -like "*.tar.xz") { "[XZ]" } else { "[ZST]" }
    Write-Host "  $type $($file.Name)"
}

# Final comprehensive status
$packageStatus = @{
    "wget" = ($allFiles | Where-Object { $_.Name -like "*wget*" }).Count -gt 0
    "make" = ($allFiles | Where-Object { $_.Name -like "*make*" }).Count -gt 0
    "flex" = ($allFiles | Where-Object { $_.Name -like "*flex*" }).Count -gt 0
    "bison" = ($allFiles | Where-Object { $_.Name -like "*bison*" }).Count -gt 0
    "git" = ($allFiles | Where-Object { $_.Name -like "*git*" }).Count -gt 0
    "gcc-core" = ($allFiles | Where-Object { $_.Name -like "*gcc-core*" }).Count -gt 0
    "gcc-g++" = ($allFiles | Where-Object { $_.Name -like "*gcc-g++*" }).Count -gt 0
    "libgcc1" = ($allFiles | Where-Object { $_.Name -like "*libgcc1*" }).Count -gt 0
    "zlib" = ($allFiles | Where-Object { $_.Name -like "*zlib*" }).Count -gt 0
    "autoconf" = ($allFiles | Where-Object { $_.Name -like "*autoconf*" }).Count -gt 0
}

Write-Host ""
Write-Host "=========================================="
Write-Host "FINAL PACKAGE STATUS"
Write-Host "=========================================="

$availableCount = 0
foreach ($pkg in $packageStatus.Keys) {
    $status = if ($packageStatus[$pkg]) { 
        $availableCount++
        "AVAILABLE" 
    } else { 
        "MISSING" 
    }
    Write-Host "$pkg : $status"
}

Write-Host ""
Write-Host "FINAL SCORE: $availableCount/10 packages available"

if ($availableCount -eq 10) {
    Write-Host ""
    Write-Host "🎉 PERFECT! ALL PACKAGES AVAILABLE!"
    Write-Host "You have everything needed for Verilator installation!"
} elseif ($availableCount -ge 9) {
    Write-Host ""
    Write-Host "🎯 EXCELLENT! Almost all packages available!"
    Write-Host "You should be able to install Verilator successfully!"
} elseif ($availableCount -ge 8) {
    Write-Host ""
    Write-Host "✅ VERY GOOD! Most packages available!"
    Write-Host "Try proceeding - you likely have enough for Verilator!"
} else {
    Write-Host ""
    Write-Host "⚠️  Some packages missing, but try proceeding anyway!"
}

Write-Host ""
Write-Host "=========================================="
Write-Host "READY TO INSTALL!"
Write-Host "=========================================="
Write-Host "Next steps:"
Write-Host "1. copy_to_cygwin.bat"
Write-Host "2. bash install_downloaded_packages.sh"
Write-Host "3. Verify: gcc --version && make --version"
Write-Host "4. Install Verilator!"
Write-Host ""
Write-Host "You're ready to build Verilator! 🚀"
