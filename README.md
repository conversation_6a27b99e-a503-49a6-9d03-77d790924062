# Cygwin Package Download and Installation for Verilator

This repository contains scripts and packages to help you install Verilator dependencies on <PERSON>g<PERSON> without admin access.

## 📁 What's Included

### Downloaded Packages (✅ Ready to Install)
- `cygwin_binary_packages/wget-1.25.0-1.tar.xz` - Download utility
- `cygwin_binary_packages/make-4.4-1.tar.xz` - Build automation tool  
- `cygwin_binary_packages/flex-2.6.4-2.tar.xz` - Lexical analyzer generator
- `cygwin_binary_packages/bison-3.8.2-1.tar.xz` - Parser generator
- `cygwin_binary_packages/git-2.45.1-1.tar.xz` - Version control system

### Installation Scripts
- `copy_to_cygwin.bat` - Windows batch script to copy packages to Cygwin
- `install_downloaded_packages.sh` - Bash script to extract packages in Cygwin
- `cygwin_installation_guide.md` - Comprehensive installation guide

### Download Scripts (For Reference)
- `download_cygwin_packages.ps1` - Original download script
- `download_specific_packages.ps1` - Targeted package download
- `download_from_multiple_mirrors.ps1` - Multi-mirror download attempt
- `find_missing_packages.ps1` - Package discovery script

## 🚀 Quick Start

### Step 1: Copy Packages to Cygwin
Run the Windows batch script:
```cmd
copy_to_cygwin.bat
```

### Step 2: Install Packages in Cygwin
Open Cygwin terminal and run:
```bash
bash install_downloaded_packages.sh
```

### Step 3: Verify Installation
```bash
wget --version
make --version
flex --version
bison --version
git --version
```

## ⚠️ Missing Packages

The following packages could not be downloaded automatically and need manual installation:

1. **autoconf** - Automatic configure script builder
2. **gcc-core** - C compiler
3. **gcc-g++** - C++ compiler  
4. **libgcc1** - GCC runtime library
5. **zlib0** - Compression library

## 🔧 Finding Missing Packages

### Option 1: Check What's Already Available
```bash
# Look for existing compilers
find /usr/bin -name "*gcc*" -o -name "*autoconf*" -o -name "*zlib*"
which gcc g++ cc autoconf
```

### Option 2: Manual Download
Browse these Cygwin mirrors and download missing packages:
- https://cygwin.mirror.constant.com/x86_64/release/
- https://mirrors.xmission.com/cygwin/x86_64/release/
- https://ftp.eq.uc.pt/software/pc/prog/cygwin/x86_64/release/

Look for directories:
- `autoconf/` or `autoconf2.69/` or `autoconf2.71/`
- `gcc-core/` or `gcc/`
- `gcc-g++/`
- `libgcc1/`
- `zlib0/` or `zlib/`

Download `.tar.xz` files (NOT `-src.tar.xz` files).

### Option 3: Alternative Approaches
If packages remain unavailable:
1. **Use WSL** - Windows Subsystem for Linux (often easier)
2. **Use MSYS2** - Alternative to Cygwin with better package management
3. **Use Docker** - Linux container with pre-installed tools
4. **Compile from source** - Download source code and compile manually

## 📋 Verilator Installation

After installing available packages, try Verilator installation:

```bash
# Clone Verilator
git clone https://github.com/verilator/verilator
cd verilator

# Configure (this will show what's missing)
unset VERILATOR_ROOT
git pull
autoconf  # May fail if autoconf is missing
./configure  # Will show specific missing dependencies

# If configure succeeds
make -j $(nproc)
make test
make install
```

## 🐛 Troubleshooting

### If autoconf is missing:
- Try `autoreconf` instead
- Look for `autoconf2.69` or `autoconf2.71` packages
- Download and compile autoconf from source

### If GCC is missing:
- Check for MinGW: `which x86_64-w64-mingw32-gcc`
- Try Clang: `which clang`
- Consider using alternative build environments

### If zlib is missing:
- Look for existing zlib: `find /usr -name "*zlib*" -o -name "*libz*"`
- Try proceeding without it (some features may be disabled)

### Path Issues:
```bash
echo $PATH
export PATH=$PATH:/usr/bin:/usr/local/bin
echo 'export PATH=$PATH:/usr/bin:/usr/local/bin' >> ~/.bashrc
```

## 📖 Detailed Documentation

See `cygwin_installation_guide.md` for comprehensive installation instructions and troubleshooting.

## 🎯 Success Criteria

You'll know the installation is working when:
1. All 5 downloaded packages install successfully
2. Basic tools are available: `wget`, `make`, `flex`, `bison`, `git`
3. You can run `./configure` in the Verilator directory (even if it fails, it will show specific missing dependencies)

## 💡 Tips

- Start with the packages we have - they may be sufficient for a basic Verilator build
- The `./configure` script will tell you exactly what's missing
- Some dependencies might already be installed under different names
- Consider alternative build environments if Cygwin proves too difficult

Good luck with your Verilator installation! 🚀
