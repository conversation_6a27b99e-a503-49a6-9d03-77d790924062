# PowerShell script to find the correct package names and versions for missing packages
# This script browses the Cygwin mirror to find available packages

# Cygwin mirror URL
$mirrorUrl = "https://mirrors.kernel.org/sourceware/cygwin/x86_64/release"

# Missing packages to search for
$missingPackages = @(
    "autoconf",
    "gcc-core", 
    "gcc-g++",
    "libgcc1",
    "zlib0"
)

function Find-AvailablePackages {
    param(
        [string]$packageDir
    )
    
    try {
        $url = "$mirrorUrl/$packageDir/"
        Write-Host "`nSearching in: $url"
        
        # Get directory listing
        $response = Invoke-WebRequest -Uri $url -UseBasicParsing
        $content = $response.Content
        
        # Extract .tar.xz files (excluding source packages)
        $pattern = "$packageDir-[\d\.]+-\d+\.tar\.xz"
        $matches = [regex]::Matches($content, $pattern)
        
        if ($matches.Count -gt 0) {
            $packages = $matches | ForEach-Object { $_.Value } | Where-Object { $_ -notlike "*-src*" } | Sort-Object
            Write-Host "Available packages:"
            foreach ($pkg in $packages) {
                Write-Host "  - $pkg"
            }
            return $packages
        } else {
            Write-Host "No packages found in $packageDir"
            return @()
        }
    }
    catch {
        Write-Warning "Failed to search $packageDir`: $($_.Exception.Message)"
        return @()
    }
}

# Alternative package directories to try
$alternativePackages = @{
    "autoconf" = @("autoconf", "autoconf2.69", "autoconf2.71")
    "gcc-core" = @("gcc-core", "gcc")
    "gcc-g++" = @("gcc-g++", "gcc-c++")
    "libgcc1" = @("libgcc1", "gcc-runtime")
    "zlib0" = @("zlib0", "zlib", "libzlib1")
}

Write-Host "Searching for missing Cygwin packages..."
Write-Host "This will help identify the correct package names and versions."

$foundPackages = @{}

foreach ($packageName in $missingPackages) {
    Write-Host "`n" + "="*50
    Write-Host "Searching for: $packageName"
    Write-Host "="*50
    
    $found = $false
    $searchDirs = $alternativePackages[$packageName]
    
    foreach ($searchDir in $searchDirs) {
        $packages = Find-AvailablePackages -packageDir $searchDir
        if ($packages.Count -gt 0) {
            $foundPackages[$packageName] = @{
                "dir" = $searchDir
                "packages" = $packages
                "latest" = $packages[-1]  # Last one should be latest
            }
            $found = $true
            break
        }
    }
    
    if (-not $found) {
        Write-Warning "Could not find any packages for $packageName"
    }
}

Write-Host "`n" + "="*60
Write-Host "SUMMARY - Found Packages"
Write-Host "="*60

if ($foundPackages.Count -gt 0) {
    foreach ($pkg in $foundPackages.Keys) {
        $info = $foundPackages[$pkg]
        Write-Host "`n$pkg (from $($info.dir)):"
        Write-Host "  Latest: $($info.latest)"
        Write-Host "  Download URL: $mirrorUrl/$($info.dir)/$($info.latest)"
    }
    
    Write-Host "`n" + "="*60
    Write-Host "DOWNLOAD COMMANDS"
    Write-Host "="*60
    Write-Host "Copy and paste these commands to download the missing packages:"
    Write-Host ""
    
    foreach ($pkg in $foundPackages.Keys) {
        $info = $foundPackages[$pkg]
        $url = "$mirrorUrl/$($info.dir)/$($info.latest)"
        $filename = $info.latest
        Write-Host "Invoke-WebRequest -Uri '$url' -OutFile 'cygwin_binary_packages\$filename' -UseBasicParsing"
    }
} else {
    Write-Host "No packages were found. You may need to:"
    Write-Host "1. Check if the package names have changed"
    Write-Host "2. Browse the Cygwin mirror manually"
    Write-Host "3. Use a different mirror"
}

Write-Host "`n" + "="*60
Write-Host "NEXT STEPS"
Write-Host "="*60
Write-Host "1. Run the download commands above"
Write-Host "2. Copy all .tar.xz files to your Cygwin home directory"
Write-Host "3. In Cygwin terminal, run:"
Write-Host "   cd ~"
Write-Host "   for file in *.tar.xz; do tar -xvf `$file -C /; done"
Write-Host "4. Verify with: wget --version, make --version, gcc --version"
