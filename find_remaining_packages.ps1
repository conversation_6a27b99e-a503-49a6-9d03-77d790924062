# Script to find the remaining packages (zlib0 and autoconf)

$downloadDir = "cygwin_binary_packages"
$baseUrl = "https://cygwin.mirror.constant.com/x86_64/release"

# Alternative paths to try for missing packages
$alternatives = @(
    # zlib alternatives
    @{
        "name" = "zlib0-1.3-1.tar.zst"
        "url" = "$baseUrl/zlib/zlib0-1.3-1.tar.zst"
    },
    @{
        "name" = "zlib0-1.2.13-1.tar.zst"
        "url" = "$baseUrl/zlib/zlib0-1.2.13-1.tar.zst"
    },
    @{
        "name" = "libzlib1-1.3.1-1.tar.zst"
        "url" = "$baseUrl/libzlib1/libzlib1-1.3.1-1.tar.zst"
    },
    
    # autoconf alternatives
    @{
        "name" = "autoconf-13-1.tar.zst"
        "url" = "$baseUrl/autoconf/autoconf-13-1.tar.zst"
    },
    @{
        "name" = "autoconf2.7-2.72-1.tar.zst"
        "url" = "$baseUrl/autoconf2.7/autoconf2.7-2.72-1.tar.zst"
    },
    @{
        "name" = "autoconf2.7-2.71-1.tar.zst"
        "url" = "$baseUrl/autoconf2.7/autoconf2.7-2.71-1.tar.zst"
    }
)

Write-Host "Searching for remaining packages..."
Write-Host ""

$successCount = 0

foreach ($alt in $alternatives) {
    $outputPath = Join-Path $downloadDir $alt.name
    
    if (Test-Path $outputPath) {
        Write-Host "SKIP: $($alt.name) already exists"
        continue
    }
    
    try {
        Write-Host "Trying: $($alt.name)"
        Write-Host "URL: $($alt.url)"
        Invoke-WebRequest -Uri $alt.url -OutFile $outputPath -UseBasicParsing -TimeoutSec 30
        Write-Host "SUCCESS: Downloaded $($alt.name)"
        $successCount++
        Write-Host ""
        
        # If we got zlib, we can stop trying other zlib variants
        if ($alt.name -like "*zlib*") {
            Write-Host "Found zlib package, skipping other zlib variants"
            break
        }
        
        # If we got autoconf, we can stop trying other autoconf variants
        if ($alt.name -like "*autoconf*") {
            Write-Host "Found autoconf package, skipping other autoconf variants"
            break
        }
    }
    catch {
        Write-Host "FAILED: $($alt.name) - $($_.Exception.Message)"
        if (Test-Path $outputPath) {
            Remove-Item $outputPath -Force
        }
    }
    Write-Host ""
}

Write-Host "Additional downloads completed: $successCount packages"

# Show final summary
$allFiles = Get-ChildItem $downloadDir -Filter "*.tar.*" -ErrorAction SilentlyContinue
Write-Host ""
Write-Host "=========================================="
Write-Host "COMPLETE PACKAGE INVENTORY"
Write-Host "=========================================="
Write-Host "Total packages available: $($allFiles.Count)"
Write-Host ""

$xzFiles = $allFiles | Where-Object { $_.Name -like "*.tar.xz" }
$zstFiles = $allFiles | Where-Object { $_.Name -like "*.tar.zst" }

Write-Host ".tar.xz packages (original downloads):"
foreach ($file in $xzFiles) {
    Write-Host "  + $($file.Name)"
}

Write-Host ""
Write-Host ".tar.zst packages (new downloads):"
foreach ($file in $zstFiles) {
    Write-Host "  + $($file.Name)"
}

Write-Host ""
Write-Host "=========================================="
Write-Host "PACKAGE STATUS"
Write-Host "=========================================="

$packageStatus = @{
    "wget" = ($allFiles | Where-Object { $_.Name -like "*wget*" }).Count -gt 0
    "make" = ($allFiles | Where-Object { $_.Name -like "*make*" }).Count -gt 0
    "flex" = ($allFiles | Where-Object { $_.Name -like "*flex*" }).Count -gt 0
    "bison" = ($allFiles | Where-Object { $_.Name -like "*bison*" }).Count -gt 0
    "git" = ($allFiles | Where-Object { $_.Name -like "*git*" }).Count -gt 0
    "gcc-core" = ($allFiles | Where-Object { $_.Name -like "*gcc-core*" }).Count -gt 0
    "gcc-g++" = ($allFiles | Where-Object { $_.Name -like "*gcc-g++*" }).Count -gt 0
    "libgcc1" = ($allFiles | Where-Object { $_.Name -like "*libgcc1*" }).Count -gt 0
    "zlib" = ($allFiles | Where-Object { $_.Name -like "*zlib*" }).Count -gt 0
    "autoconf" = ($allFiles | Where-Object { $_.Name -like "*autoconf*" }).Count -gt 0
}

foreach ($pkg in $packageStatus.Keys) {
    $status = if ($packageStatus[$pkg]) { "AVAILABLE" } else { "MISSING" }
    Write-Host "$pkg : $status"
}

$availableCount = ($packageStatus.Values | Where-Object { $_ -eq $true }).Count
$totalCount = $packageStatus.Count

Write-Host ""
Write-Host "Summary: $availableCount/$totalCount packages available"

if ($availableCount -eq $totalCount) {
    Write-Host ""
    Write-Host "EXCELLENT! All packages are now available!"
    Write-Host "You can proceed with the complete Verilator installation."
} elseif ($availableCount -ge 8) {
    Write-Host ""
    Write-Host "VERY GOOD! You have most packages."
    Write-Host "You can try proceeding with Verilator installation."
    Write-Host "Missing packages might not be critical or might have alternatives."
} else {
    Write-Host ""
    Write-Host "You have the basic packages but some are still missing."
    Write-Host "Try proceeding anyway - Verilator's configure script will tell you what's essential."
}
